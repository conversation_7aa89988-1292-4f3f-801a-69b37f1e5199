# Firebase Setup Guide

This guide will help you set up Firebase Authentication and Firestore for the Restaurant Order Admin app.

## Prerequisites

- A Google account
- Access to the [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter a project name (e.g., "restaurant-order-admin")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project, go to "Authentication" in the left sidebar
2. Click "Get started"
3. Go to the "Sign-in method" tab
4. Enable "Email/Password" authentication:
   - Click on "Email/Password"
   - Toggle "Enable" to ON
   - Click "Save"

## Step 3: Create Test User

1. Go to the "Users" tab in Authentication
2. Click "Add user"
3. Enter email: `<EMAIL>`
4. Enter password: `admin123`
5. Click "Add user"

## Step 4: Set up Firestore Database

1. Go to "Firestore Database" in the left sidebar
2. Click "Create database"
3. <PERSON>ose "Start in test mode" (for development)
4. Select a location for your database
5. Click "Done"

## Step 5: Get Firebase Configuration

1. Go to Project Settings (gear icon in the left sidebar)
2. Scroll down to "Your apps" section
3. Click the web icon (`</>`) to add a web app
4. Enter an app nickname (e.g., "restaurant-admin-web")
5. Click "Register app"
6. Copy the Firebase configuration object

## Step 6: Update Environment Files

Replace the placeholder values in your environment files with your actual Firebase configuration:

### `src/environments/environment.ts`
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  firebase: {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
  }
};
```

### `src/environments/environment.prod.ts`
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://api.restaurant-admin.com/api',
  firebase: {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
  }
};
```

## Step 7: Set up Firestore Security Rules (Optional)

For production, you should set up proper security rules. Go to Firestore Database > Rules and update:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write orders
    match /orders/{document} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Step 8: Test the Setup

1. Run your app: `npm start`
2. Navigate to the login page
3. Use the test credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`
4. You should be able to log in and access the orders page

## Firestore Data Structure

The app expects orders to be stored in a `orders` collection with the following structure:

```javascript
{
  id: "order-id",
  tableNumber: "12",
  items: [
    {
      id: "item-id",
      name: "Pizza Margherita",
      quantity: 1,
      price: 12.99,
      modifiers: [
        {
          id: "modifier-id",
          name: "Extra Cheese",
          price: 1.50
        }
      ]
    }
  ],
  status: "pending", // "pending", "completed", "rejected"
  createdAt: Timestamp,
  updatedAt: Timestamp,
  total: 14.49,
  customerName: "John Doe",
  specialInstructions: "No onions",
  rejectionReason: "out_of_stock", // only if rejected
  rejectionNote: "Ingredients not available" // only if rejected
}
```

## Troubleshooting

### Common Issues:

1. **Authentication not working**: Check that Email/Password is enabled in Firebase Console
2. **Firestore permission denied**: Ensure you're authenticated and security rules allow access
3. **Configuration errors**: Double-check that all Firebase config values are correct
4. **CORS issues**: Make sure your domain is added to Firebase authorized domains

### Debug Tips:

- Check browser console for error messages
- Verify Firebase configuration in environment files
- Test authentication in Firebase Console
- Check Firestore rules and data structure

## Next Steps

- Set up proper Firestore security rules for production
- Add more authentication methods if needed
- Set up Firebase hosting for deployment
- Configure Firebase Functions for server-side logic (optional)
