# Complete Database Setup Guide for Order Flow QR

This guide will walk you through setting up the complete Firestore database for your Order Flow QR system.

## 📋 Overview

Your database will have 6 main collections:
- **venues** - Restaurant/venue information
- **waiters** - Staff members who can manage orders  
- **tables** - Physical tables with QR codes
- **products** - Menu items and modifiers
- **orders** - Customer orders
- **order_items** - Individual items within orders

## 🚀 Quick Setup (Recommended)

### Step 1: Firebase Project Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Enable **Authentication** with Email/Password
4. Enable **Firestore Database** in test mode
5. Get your Firebase configuration from Project Settings

### Step 2: Update Environment Files

Update your `src/environments/environment.ts` and `environment.prod.ts` with your Firebase config:

```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com", 
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
  }
};
```

### Step 3: Create Admin User in Firebase Auth

1. Go to Firebase Console > Authentication > Users
2. Click "Add user"
3. Email: `<EMAIL>`
4. Password: `admin123`
5. Click "Add user"

### Step 4: Run Database Initialization Script

1. Update the Firebase config in `scripts/init-firestore-database.ts`
2. Install dependencies: `npm install firebase`
3. Run the script: `npx ts-node scripts/init-firestore-database.ts`

This will create all collections with sample data including:
- 1 venue (Bella Vista Restaurant)
- 1 admin user
- 10 tables with QR codes
- 5 sample menu products
- 1 sample order with items

### Step 5: Set Up Security Rules

Go to Firestore Database > Rules and replace with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write all collections
    // TODO: Implement proper role-based security for production
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Step 6: Create Firestore Indexes

Go to Firestore Database > Indexes and create these composite indexes:

1. **Collection: orders**
   - Fields: `venueId` (Ascending), `status` (Ascending), `createdAt` (Descending)
   - Fields: `venueId` (Ascending), `tableId` (Ascending), `createdAt` (Descending)

2. **Collection: products**  
   - Fields: `venueId` (Ascending), `category` (Ascending), `sortOrder` (Ascending)
   - Fields: `venueId` (Ascending), `isActive` (Ascending)

3. **Collection: order_items**
   - Fields: `orderId` (Ascending), `status` (Ascending)

## 🧪 Test Your Setup

1. Start your app: `npm start`
2. Navigate to login page
3. Login with: `<EMAIL>` / `admin123`
4. You should see the sample order in the orders list
5. Try updating order status to test functionality

## 📊 Data Structure Summary

### Key Relationships
- **Venues** contain **Tables** and **Products**
- **Waiters** belong to **Venues** and can manage **Orders**
- **Orders** reference **Tables** and contain **Order Items**
- **Order Items** reference **Products** and store selected modifiers

### Important Fields
- All collections have `venueId` for multi-tenant support
- Orders use `tableNumber` (denormalized) for quick display
- Products have complex `modifiers` array for customization
- Order items store `selectedModifiers` for historical data
- All documents have `isActive` for soft deletes

## 🔧 Customization

### Adding More Sample Data

Edit `scripts/init-firestore-database.ts` to add:
- More tables
- Additional menu categories and products
- Different modifier types (size, extras, cooking preferences)
- Multiple venues for testing

### Modifying Data Structure

See `FIRESTORE_DATABASE_SETUP.md` for complete TypeScript interfaces and detailed field descriptions.

### Security Rules for Production

The current rules allow all authenticated users full access. For production, implement role-based security using the `waiters` collection to check permissions.

## 🚨 Important Notes

- **Currency**: All prices stored as numbers (25.99 for 25.99 RON)
- **Timestamps**: Use Firestore Timestamp objects
- **QR Codes**: Each table has a unique QR code URL
- **Order Numbers**: Human-readable format (ORD-001, ORD-002, etc.)
- **Soft Deletes**: Use `isActive: false` instead of deleting documents

## 📱 QR Code Integration

Each table has a QR code that links to:
```
https://your-customer-app.com/order?table=table_001
```

This allows customers to:
1. Scan QR code at their table
2. Browse menu and place orders
3. Orders appear in your admin app automatically

## 🔄 Next Steps

1. **Test the complete flow** with sample data
2. **Customize the menu** with your actual products
3. **Set up proper security rules** for production
4. **Generate real QR codes** for your tables
5. **Deploy your customer-facing ordering app**

## 📞 Support

If you encounter issues:
1. Check Firebase Console for authentication/database errors
2. Verify all environment variables are set correctly
3. Ensure Firestore rules allow your operations
4. Check browser console for detailed error messages

Your Order Flow QR system is now ready to manage restaurant orders efficiently! 🎉
