import { Component, OnInit } from '@angular/core';
import { AuthService } from './services/auth.service';
import { Observable } from 'rxjs';

@Component({
    selector: 'app-root',
    templateUrl: 'app.component.html',
    styleUrls: ['app.component.scss'],
    standalone: false
})
export class AppComponent implements OnInit {
  authStateInitialized$: Observable<boolean>;

  constructor(private authService: AuthService) {
    this.authStateInitialized$ = this.authService.authStateInitialized$;
  }

  ngOnInit() {
    // Auth service will automatically initialize when the component loads
  }
}
