import { Injectable } from '@angular/core';
import { Auth, signInWithEmailAndPassword, signOut, User, onAuthStateChanged } from '@angular/fire/auth';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  private authStateInitialized = new BehaviorSubject<boolean>(false);
  public authStateInitialized$ = this.authStateInitialized.asObservable();

  constructor(
    private auth: Auth,
    private router: Router,
    private toastService: ToastService
  ) {
    // Listen to auth state changes
    onAuthStateChanged(this.auth, (user) => {
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(!!user);

      // Mark auth state as initialized after first emission
      if (!this.authStateInitialized.value) {
        this.authStateInitialized.next(true);
      }
    });
  }

  async login(email: string, password: string): Promise<boolean> {
    try {
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      if (userCredential.user) {
        // No toast notification for successful login
        this.router.navigate(['/tabs/orders']);
        return true;
      }
      return false;
    } catch (error: any) {
      let errorMessage = 'Login failed. Please try again.';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Incorrect password.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address.';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'auth/invalid-credential':
          errorMessage = 'Invalid email or password.';
          break;
      }

      await this.toastService.presentErrorToast(errorMessage);
      return false;
    }
  }

  async logout(): Promise<void> {
    try {
      await signOut(this.auth);
      await this.toastService.presentSuccessToast('Logged out successfully');
      this.router.navigate(['/login']);
    } catch (error) {
      await this.toastService.presentErrorToast('Error logging out');
    }
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  isAuthStateInitialized(): boolean {
    return this.authStateInitialized.value;
  }
}
