import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../models/order.model';
import { environment } from '../../environments/environment';

// Firestore imports
import { Firestore, collection, collectionData, doc, updateDoc, query, where, orderBy, Timestamp } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  public orders$ = this.ordersSubject.asObservable();

  // Mock data for demo purposes
  private mockOrders: Order[] = [
    {
      id: '1001',
      tableNumber: '12',
      items: [
        {
          id: 'i1',
          name: 'Margherita Pizza',
          quantity: 1,
          price: 12.99,
          modifiers: [
            { id: 'm1', name: 'Extra Cheese', price: 1.50 }
          ]
        },
        { id: 'i2', name: 'Garlic Bread', quantity: 1, price: 4.99 }
      ],
      status: OrderStatus.PENDING,
      createdAt: new Date(new Date().getTime() - 5 * 60000), // 5 minutes ago
      updatedAt: new Date(new Date().getTime() - 5 * 60000),
      total: 17.98
    },
    {
      id: '1002',
      tableNumber: '8',
      items: [
        { id: 'i3', name: 'Chicken Alfredo', quantity: 1, price: 15.99 },
        {
          id: 'i4',
          name: 'Caesar Salad',
          quantity: 1,
          price: 8.99,
          modifiers: [
            { id: 'm2', name: 'No Croutons', price: 0 }
          ]
        },
        { id: 'i5', name: 'Iced Tea', quantity: 2, price: 2.99 }
      ],
      status: OrderStatus.PENDING,
      createdAt: new Date(new Date().getTime() - 12 * 60000), // 12 minutes ago
      updatedAt: new Date(new Date().getTime() - 12 * 60000),
      total: 30.96,
      specialInstructions: 'No croutons on salad'
    },
    {
      id: '1003',
      tableNumber: '5',
      items: [
        {
          id: 'i6',
          name: 'Cheeseburger',
          quantity: 2,
          price: 10.99,
          modifiers: [
            { id: 'm3', name: 'Cheddar Cheese', price: 0.75 },
            { id: 'm4', name: 'Bacon', price: 1.50 }
          ]
        },
        { id: 'i7', name: 'French Fries', quantity: 1, price: 4.99 },
        { id: 'i8', name: 'Chocolate Milkshake', quantity: 2, price: 5.99 }
      ],
      status: OrderStatus.COMPLETED,
      createdAt: new Date(new Date().getTime() - 45 * 60000), // 45 minutes ago
      updatedAt: new Date(new Date().getTime() - 30 * 60000), // 30 minutes ago
      total: 38.95
    },
    {
      id: '1004',
      tableNumber: '3',
      items: [
        {
          id: 'i9',
          name: 'Vegetable Stir Fry',
          quantity: 1,
          price: 13.99,
          modifiers: [
            { id: 'm5', name: 'Spicy', price: 0 },
            { id: 'm6', name: 'Extra Vegetables', price: 2.00 }
          ]
        }
      ],
      status: OrderStatus.REJECTED,
      createdAt: new Date(new Date().getTime() - 60 * 60000), // 60 minutes ago
      updatedAt: new Date(new Date().getTime() - 55 * 60000), // 55 minutes ago
      total: 13.99,
      rejectionReason: RejectionReason.OUT_OF_STOCK,
      rejectionNote: 'Out of fresh vegetables'
    },
    {
      id: '1005',
      tableNumber: '9',
      items: [
        {
          id: 'i10',
          name: 'Spaghetti Bolognese',
          quantity: 1,
          price: 14.99,
          modifiers: [
            { id: 'm7', name: 'Extra Sauce', price: 1.00 }
          ]
        },
        { id: 'i11', name: 'Garlic Bread', quantity: 1, price: 4.99 },
        { id: 'i12', name: 'Tiramisu', quantity: 1, price: 6.99 }
      ],
      status: OrderStatus.PENDING,
      createdAt: new Date(), // Just now
      updatedAt: new Date(),
      total: 26.97,
      customerName: 'John Smith'
    }
  ];

  constructor(
    private http: HttpClient,
    private firestore: Firestore
  ) {
    // Initialize with mock data for demo
    this.ordersSubject.next(this.mockOrders);

    // Load orders from Firestore
    this.loadOrdersFromFirestore();
  }

  private loadOrdersFromFirestore() {
    const ordersCollection = collection(this.firestore, 'orders');
    const ordersQuery = query(ordersCollection, orderBy('createdAt', 'desc'));

    collectionData(ordersQuery, { idField: 'id' }).subscribe((firestoreOrders: any[]) => {
      if (firestoreOrders.length > 0) {
        // Convert Firestore data to Order objects
        const orders: Order[] = firestoreOrders.map(order => ({
          ...order,
          createdAt: order.createdAt?.toDate() || new Date(),
          updatedAt: order.updatedAt?.toDate() || new Date()
        }));
        this.ordersSubject.next(orders);
      }
    });
  }

  getOrders(): Observable<Order[]> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order[]>(`${environment.apiUrl}/orders`).pipe(
    //   tap(orders => this.ordersSubject.next(orders))
    // );

    // Using mock data for demo
    return of(this.mockOrders);
  }

  getOrderById(id: string): Observable<Order | undefined> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order>(`${environment.apiUrl}/orders/${id}`);

    // Using mock data for demo
    return of(this.mockOrders.find(order => order.id === id));
  }

  getPendingOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.PENDING)
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      )
    );
  }

  getCompletedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.COMPLETED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  getRejectedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.REJECTED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  async acceptOrder(orderId: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.COMPLETED,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.COMPLETED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error accepting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.COMPLETED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }

  async rejectOrder(orderId: string, reason: RejectionReason, note?: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.REJECTED,
        rejectionReason: reason,
        rejectionNote: note,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error rejecting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }
}
