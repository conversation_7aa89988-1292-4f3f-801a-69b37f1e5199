import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../models/order.model';
import { environment } from '../../environments/environment';

// Firestore imports
import { Firestore, collection, collectionData, doc, updateDoc, query, where, orderBy, Timestamp } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  public orders$ = this.ordersSubject.asObservable();

  // Mock data for demo purposes
  private mockOrders: Order[] = [
    {
      id: '1001',
      venueId: 'venue_001',
      tableId: 'table_001',
      tableName: 'Table 12',
      orderNumber: 'ORD-1001',
      items: [
        {
          id: 'i1',
          name: 'Margherita Pizza',
          quantity: 1,
          price: 14.49,
          notes: 'Extra cheese'
        },
        {
          id: 'i2',
          name: 'Garlic Bread',
          quantity: 1,
          price: 4.99
        }
      ],
      status: OrderStatus.PENDING,
      total: 19.48,
      createdAt: new Date(new Date().getTime() - 5 * 60000), // 5 minutes ago
      updatedAt: new Date(new Date().getTime() - 5 * 60000)
    },
    {
      id: '1002',
      venueId: 'venue_001',
      tableId: 'table_002',
      tableName: 'Table 8',
      orderNumber: 'ORD-1002',
      items: [
        {
          id: 'i3',
          name: 'Chicken Alfredo',
          quantity: 1,
          price: 15.99
        }
      ],
      status: OrderStatus.PENDING,
      total: 15.99,
      specialInstructions: 'No croutons on salad',
      createdAt: new Date(new Date().getTime() - 12 * 60000), // 12 minutes ago
      updatedAt: new Date(new Date().getTime() - 12 * 60000)
    }
  ];

  constructor(
    private http: HttpClient,
    private firestore: Firestore
  ) {
    // Initialize with mock data for demo
    this.ordersSubject.next(this.mockOrders);

    // Load orders from Firestore
    this.loadOrdersFromFirestore();
  }

  private loadOrdersFromFirestore() {
    const ordersCollection = collection(this.firestore, 'orders');
    const ordersQuery = query(ordersCollection, orderBy('createdAt', 'desc'));

    collectionData(ordersQuery, { idField: 'id' }).subscribe((firestoreOrders: any[]) => {
      if (firestoreOrders.length > 0) {
        // Convert Firestore data to Order objects
        const orders: Order[] = firestoreOrders.map(order => ({
          ...order,
          createdAt: order.createdAt?.toDate() || new Date(),
          updatedAt: order.updatedAt?.toDate() || new Date()
        }));
        this.ordersSubject.next(orders);
      }
    });
  }

  getOrders(): Observable<Order[]> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order[]>(`${environment.apiUrl}/orders`).pipe(
    //   tap(orders => this.ordersSubject.next(orders))
    // );

    // Using mock data for demo
    return of(this.mockOrders);
  }

  getOrderById(id: string): Observable<Order | undefined> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order>(`${environment.apiUrl}/orders/${id}`);

    // Using mock data for demo
    return of(this.mockOrders.find(order => order.id === id));
  }

  getPendingOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.PENDING)
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      )
    );
  }

  getCompletedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.CONFIRMED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  getRejectedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.REJECTED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  async acceptOrder(orderId: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.CONFIRMED,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.CONFIRMED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error accepting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.CONFIRMED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }

  async rejectOrder(orderId: string, reason: RejectionReason, note?: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.REJECTED,
        rejectionReason: reason,
        rejectionNote: note,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error rejecting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }
}
