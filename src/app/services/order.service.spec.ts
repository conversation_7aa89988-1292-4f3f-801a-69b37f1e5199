import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { OrderService } from './order.service';
import { OrderStatus, RejectionReason } from '../models/order.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('OrderService', () => {
  let service: OrderService;

  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [OrderService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]
});
    service = TestBed.inject(OrderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get pending orders', (done) => {
    service.getPendingOrders().subscribe(orders => {
      expect(orders.length).toBeGreaterThanOrEqual(0);
      orders.forEach(order => {
        expect(order.status).toBe(OrderStatus.PENDING);
      });
      done();
    });
  });

  it('should get completed orders', (done) => {
    service.getCompletedOrders().subscribe(orders => {
      expect(orders.length).toBeGreaterThanOrEqual(0);
      orders.forEach(order => {
        expect(order.status).toBe(OrderStatus.COMPLETED);
      });
      done();
    });
  });

  it('should get rejected orders', (done) => {
    service.getRejectedOrders().subscribe(orders => {
      expect(orders.length).toBeGreaterThanOrEqual(0);
      orders.forEach(order => {
        expect(order.status).toBe(OrderStatus.REJECTED);
      });
      done();
    });
  });

  it('should accept an order', (done) => {
    // Get a pending order first
    service.getPendingOrders().subscribe(pendingOrders => {
      if (pendingOrders.length > 0) {
        const orderId = pendingOrders[0].id;
        
        service.acceptOrder(orderId).subscribe(updatedOrder => {
          expect(updatedOrder.id).toBe(orderId);
          expect(updatedOrder.status).toBe(OrderStatus.COMPLETED);
          done();
        });
      } else {
        // Skip test if no pending orders
        pending('No pending orders to test with');
        done();
      }
    });
  });

  it('should reject an order', (done) => {
    // Get a pending order first
    service.getPendingOrders().subscribe(pendingOrders => {
      if (pendingOrders.length > 0) {
        const orderId = pendingOrders[0].id;
        const reason = RejectionReason.OUT_OF_STOCK;
        const note = 'Test rejection note';
        
        service.rejectOrder(orderId, reason, note).subscribe(updatedOrder => {
          expect(updatedOrder.id).toBe(orderId);
          expect(updatedOrder.status).toBe(OrderStatus.REJECTED);
          expect(updatedOrder.rejectionReason).toBe(reason);
          expect(updatedOrder.rejectionNote).toBe(note);
          done();
        });
      } else {
        // Skip test if no pending orders
        pending('No pending orders to test with');
        done();
      }
    });
  });
});
