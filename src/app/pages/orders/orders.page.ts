import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../../models/order.model';
import { LoadingService } from '../../services/loading.service';
import { OrderService } from '../../services/order.service';
import { ToastService } from '../../services/toast.service';
import { UtilityService } from '../../services/utility.service';
import { RejectReasonModalComponent } from '../../components/reject-reason-modal/reject-reason-modal.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-orders',
  templateUrl: './orders.page.html',
  styleUrls: ['./orders.page.scss'],
  standalone: false
})
export class OrdersPage implements OnInit {
  pendingOrders$: Observable<Order[]>;

  constructor(
    private orderService: OrderService,
    private modalController: ModalController,
    private utilityService: UtilityService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    private router: Router
  ) {
    this.pendingOrders$ = this.orderService.getPendingOrders();
  }

  ngOnInit() {
    // Load orders on init
    this.loadOrders();
  }

  async loadOrders() {
    await this.loadingService.present('Loading orders...');
    this.orderService.getOrders()
      .pipe(finalize(() => this.loadingService.dismiss()))
      .subscribe({
        error: (err) => {
          this.toastService.presentErrorToast('Failed to load orders');
          console.error('Error loading orders:', err);
        }
      });
  }

  async acceptOrder(order: Order) {
    await this.loadingService.present('Accepting order...');
    this.orderService.acceptOrder(order.id)
      .pipe(finalize(() => this.loadingService.dismiss()))
      .subscribe({
        error: (err) => {
          this.toastService.presentErrorToast('Failed to accept order');
          console.error('Error accepting order:', err);
        }
      });
  }

  async rejectOrder(order: Order) {
    const modal = await this.modalController.create({
      component: RejectReasonModalComponent,
      cssClass: 'reject-reason-modal',
      backdropDismiss: false
    });

    await modal.present();

    const { data } = await modal.onWillDismiss();

    if (data && data.reason) {
      await this.loadingService.present('Rejecting order...');
      this.orderService.rejectOrder(
        order.id,
        data.reason,
        data.note
      )
        .pipe(finalize(() => this.loadingService.dismiss()))
        .subscribe({
          next: () => {
            // Removed success toast on reject
          },
          error: (err) => {
            this.toastService.presentErrorToast('Failed to reject order');
            console.error('Error rejecting order:', err);
          }
        });
    }
  }

  getTimeAgo(date: Date): string {
    return this.utilityService.getTimeAgo(date);
  }

  formatCurrency(value: number): string {
    return this.utilityService.formatCurrency(value);
  }

  viewOrderDetails(order: Order) {
    this.router.navigate(['/order-detail', order.id]);
  }

  doRefresh(event: any) {
    this.orderService.getOrders().subscribe({
      next: () => {
        event.target.complete();
      },
      error: (err) => {
        event.target.complete();
        this.toastService.presentErrorToast('Failed to refresh orders');
        console.error('Error refreshing orders:', err);
      }
    });
  }
}
