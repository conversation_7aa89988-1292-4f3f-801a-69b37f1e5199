import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Order, OrderStatus } from '../../models/order.model';
import { LoadingService } from '../../services/loading.service';
import { OrderService } from '../../services/order.service';
import { ToastService } from '../../services/toast.service';
import { UtilityService } from '../../services/utility.service';

@Component({
    selector: 'app-history',
    templateUrl: './history.page.html',
    styleUrls: ['./history.page.scss'],
    standalone: false
})
export class HistoryPage implements OnInit {
  OrderStatus = OrderStatus; // Expose OrderStatus enum to the template
  completedOrders$: Observable<Order[]>;
  rejectedOrders$: Observable<Order[]>;
  segmentValue = 'completed';

  constructor(
    private orderService: OrderService,
    private router: Router,
    private utilityService: UtilityService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) {
    this.completedOrders$ = this.orderService.getCompletedOrders();
    this.rejectedOrders$ = this.orderService.getRejectedOrders();
  }

  ngOnInit() {
    // Load orders on init
    this.loadOrders();
  }

  async loadOrders() {
    await this.loadingService.present('Loading order history...');
    this.orderService.getOrders()
      .pipe(finalize(() => this.loadingService.dismiss()))
      .subscribe({
        error: (err) => {
          this.toastService.presentErrorToast('Failed to load order history');
          console.error('Error loading order history:', err);
        }
      });
  }

  viewOrderDetails(order: Order) {
    this.router.navigate(['/order-detail', order.id]);
  }

  getTimeAgo(date: Date): string {
    return this.utilityService.getTimeAgo(date);
  }

  formatCurrency(value: number): string {
    return this.utilityService.formatCurrency(value);
  }

  doRefresh(event: any) {
    this.orderService.getOrders().subscribe({
      next: () => {
        event.target.complete();
      },
      error: (err) => {
        event.target.complete();
        this.toastService.presentErrorToast('Failed to refresh order history');
        console.error('Error refreshing order history:', err);
      }
    });
  }
}
