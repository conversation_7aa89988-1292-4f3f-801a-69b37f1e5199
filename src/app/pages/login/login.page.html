<ion-content class="login-content">
  <div class="login-container">
    <!-- Logo/Header Section -->
    <div class="header-section">
      <ion-icon name="restaurant" class="logo-icon"></ion-icon>
      <h1>Restaurant Admin</h1>
      <p>Sign in to manage orders</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- Email Field -->
      <ion-item class="form-item" [class.ion-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
        <ion-label position="stacked">Email</ion-label>
        <ion-input
          type="email"
          formControlName="email"
          placeholder="Enter your email"
          autocomplete="email">
        </ion-input>
      </ion-item>
      <div class="error-message" *ngIf="getErrorMessage('email')">
        {{ getErrorMessage('email') }}
      </div>

      <!-- Password Field -->
      <ion-item class="form-item" [class.ion-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
        <ion-label position="stacked">Password</ion-label>
        <ion-input
          [type]="showPassword ? 'text' : 'password'"
          formControlName="password"
          placeholder="Enter your password"
          autocomplete="current-password">
        </ion-input>
        <ion-button
          fill="clear"
          slot="end"
          (click)="togglePasswordVisibility()"
          class="password-toggle">
          <ion-icon [name]="showPassword ? 'eye-off' : 'eye'"></ion-icon>
        </ion-button>
      </ion-item>
      <div class="error-message" *ngIf="getErrorMessage('password')">
        {{ getErrorMessage('password') }}
      </div>

      <!-- Login Button -->
      <ion-button
        expand="block"
        type="submit"
        class="login-button"
        [disabled]="loginForm.invalid">
        <ion-icon name="log-in" slot="start"></ion-icon>
        Sign In
      </ion-button>
    </form>

    <!-- Demo Credentials Info -->
    <div class="demo-info">
      <ion-card>
        <ion-card-header>
          <ion-card-title>Demo Credentials</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p><strong>Email:</strong> admin&#64;restaurant.com</p>
          <p><strong>Password:</strong> admin123</p>
          <p class="note">Use these credentials for testing</p>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
