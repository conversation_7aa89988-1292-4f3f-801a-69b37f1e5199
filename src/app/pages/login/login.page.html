<ion-content class="login-content">
  <div class="login-wrapper">
    <!-- Login Side -->
    <div class="login-side">
      <div class="login-container">
        <!-- Logo/Header Section -->
        <header class="header-section">
          <ion-icon name="restaurant" class="logo-icon" aria-hidden="true"></ion-icon>
          <h1>Restaurant Admin</h1>
          <p>Sign in to manage orders</p>
        </header>

        <!-- Login Form -->
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- General Login Error -->
          <div class="error-message login-error" *ngIf="loginError">
            {{ loginError }}
          </div>

          <!-- Email Field -->
          <ion-item class="form-item" [class.ion-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
            <ion-input
              labelPlacement="stacked"
              label="Email"
              type="email"
              formControlName="email"
              placeholder="Enter your email"
              autocomplete="email"
              aria-describedby="email-error">
            </ion-input>
          </ion-item>
          <div class="error-message" id="email-error" *ngIf="getErrorMessage('email')">
            {{ getErrorMessage('email') }}
          </div>

          <!-- Password Field -->
          <ion-item class="form-item" [class.ion-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
            <ion-input
              labelPlacement="stacked"
              label="Password"
              [type]="showPassword ? 'text' : 'password'"
              formControlName="password"
              placeholder="Enter your password"
              autocomplete="current-password"
              aria-describedby="password-error">
              <ion-button
                fill="clear"
                slot="end"
                (click)="togglePasswordVisibility()"
                aria-label="Show/hide password">
                <ion-icon
                  slot="icon-only"
                  [name]="showPassword ? 'eye-off' : 'eye'"
                  aria-hidden="true">
                </ion-icon>
              </ion-button>
            </ion-input>
          </ion-item>
          <div class="error-message" id="password-error" *ngIf="getErrorMessage('password')">
            {{ getErrorMessage('password') }}
          </div>

          <!-- Login Button -->
          <ion-button
            expand="block"
            type="submit"
            class="login-button"
            [disabled]="loginForm.invalid">
            <ion-icon name="log-in" slot="start"></ion-icon>
            Sign In
          </ion-button>
        </form>
      </div>
    </div>

    <!-- Image Side -->
    <div class="image-side">
      <img
        src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
        alt="Restaurant interior"
        class="restaurant-image">
      <div class="image-overlay">
        <h2 class="overlay-title">Manage Your Restaurant</h2>
        <p class="overlay-subtitle">Streamline orders, track performance, and grow your business</p>
      </div>
    </div>
  </div>
</ion-content>
