<ion-content class="login-content">
  <div class="login-container">
    <!-- Logo/Header Section -->
    <header class="header-section">
      <ion-icon name="restaurant" class="logo-icon" aria-hidden="true"></ion-icon>
      <h1>Restaurant Admin</h1>
      <p>Sign in to manage orders</p>
    </header>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- General <PERSON><PERSON> Error -->
      <div class="error-message login-error" *ngIf="loginError">
        {{ loginError }}
      </div>

      <!-- Email Field -->
      <ion-item class="form-item" [class.ion-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
        <ion-label position="stacked">Email</ion-label>
        <ion-input
          type="email"
          formControlName="email"
          placeholder="Enter your email"
          autocomplete="email"
          aria-describedby="email-error">
        </ion-input>
      </ion-item>
      <div class="error-message" id="email-error" *ngIf="getErrorMessage('email')">
        {{ getErrorMessage('email') }}
      </div>

      <!-- Password Field -->
      <ion-item class="form-item" [class.ion-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
        <ion-label position="stacked">Password</ion-label>
        <ion-input
          [type]="showPassword ? 'text' : 'password'"
          formControlName="password"
          placeholder="Enter your password"
          autocomplete="current-password"
          aria-describedby="password-error">
        </ion-input>
        <ion-button
          fill="clear"
          slot="end"
          (click)="togglePasswordVisibility()"
          class="password-toggle">
          <ion-icon [name]="showPassword ? 'eye-off' : 'eye'"></ion-icon>
        </ion-button>
      </ion-item>
      <div class="error-message" id="password-error" *ngIf="getErrorMessage('password')">
        {{ getErrorMessage('password') }}
      </div>

      <!-- Login Button -->
      <ion-button
        expand="block"
        type="submit"
        class="login-button"
        [disabled]="loginForm.invalid">
        <ion-icon name="log-in" slot="start"></ion-icon>
        Sign In
      </ion-button>
    </form>


  </div>
</ion-content>
