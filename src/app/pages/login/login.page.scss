ion-content.login-content {
  --background: #1a1a1a;
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

.login-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.login-side {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #1a1a1a;
}

.image-side {
  flex: 1;
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.restaurant-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.7;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  text-align: center;
  padding: 40px;
}

.overlay-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--ion-color-primary);
}

.overlay-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 32px;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;

  .logo-icon {
    font-size: 64px;
    color: var(--ion-color-primary);
    margin-bottom: 16px;
  }

  h1 {
    color: #ffffff;
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    color: #a0a0a0;
    font-size: 16px;
    margin: 0;
  }
}

.login-form {
  margin-bottom: 24px;

  .form-item {
    margin-bottom: 20px;
    --background: #2d2d2d;
    --border-radius: 0px;
    --border-color: transparent;
    --color: #ffffff;
    --min-height: 52px;
    transition: all 0.2s ease;

    &.ion-focused {
      --background: #363636;
      --border-color: var(--ion-color-primary);
    }

    &.ion-invalid.ion-touched {
      --background: rgba(255, 77, 77, 0.1);
      --border-color: var(--ion-color-danger);
    }

    ion-input {
      --color: #ffffff;
      --placeholder-color: #888888;
      font-size: 16px;

      ion-button {
        --color: #888888;
        margin: 0;

        &:hover {
          --color: #ffffff;
        }

        ion-icon {
          font-size: 18px;
        }
      }
    }
  }

  .error-message {
    color: var(--ion-color-danger);
    font-size: 13px;
    margin: 6px 16px 20px 16px;
    min-height: 18px;

    &::before {
      content: '⚠ ';
    }

    &.login-error {
      background: rgba(255, 77, 77, 0.1);
      border: 1px solid var(--ion-color-danger);
      border-radius: 8px;
      padding: 12px 16px;
      margin: 0 0 20px 0;
      text-align: center;
      font-weight: 500;

      &::before {
        content: '🚫 ';
      }
    }
  }

  .login-button {
    margin-top: 24px;
    --border-radius: 8px;
    height: 48px;
    font-weight: 600;
    font-size: 16px;
    --background: var(--ion-color-primary);

    &[disabled] {
      --opacity: 0.6;
    }

    ion-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}



// Responsive design
@media (max-width: 767px) {
  .login-wrapper {
    flex-direction: column;
  }

  .image-side {
    display: none;
  }

  .login-side {
    padding: 20px;
  }

  .login-container {
    padding: 24px;
    max-width: 100%;
  }

  .header-section {
    margin-bottom: 32px;
  }

  .header-section .logo-icon {
    font-size: 56px;
  }

  .header-section h1 {
    font-size: 24px;
  }

  .login-form .form-item {
    --min-height: 48px;
  }

  .login-form .login-button {
    height: 44px;
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .login-container {
    max-width: 420px;
    padding: 48px;
  }

  .login-form .form-item {
    --min-height: 56px;
    --border-radius: 8px;
  }

  .header-section .logo-icon {
    font-size: 72px;
  }

  .header-section h1 {
    font-size: 32px;
  }

  .login-form .login-button {
    height: 52px;
  }
}

@media (min-width: 1024px) {
  .login-container {
    max-width: 440px;
    padding: 56px;
  }
  .header-section .logo-icon { font-size: 80px; }
  .header-section h1 { font-size: 36px; }
  .login-form .form-item { --min-height: 60px; }
  .login-form .login-button { height: 56px; }
}
