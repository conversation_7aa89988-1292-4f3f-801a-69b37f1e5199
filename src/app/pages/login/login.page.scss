ion-content.login-content {
  --background: #1a1a1a;
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

.login-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 32px;
  background: #1e1e1e;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;

  .logo-icon {
    font-size: 64px;
    color: var(--ion-color-primary);
    margin-bottom: 16px;
  }

  h1 {
    color: #ffffff;
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    color: #a0a0a0;
    font-size: 16px;
    margin: 0;
  }
}

.login-form {
  margin-bottom: 24px;

  .form-item {
    margin-bottom: 20px;
    --background: #2d2d2d;
    --border-radius: 0px;
    --border-color: transparent;
    --color: #ffffff;
    --min-height: 52px;
    transition: all 0.2s ease;

    &.ion-focused {
      --background: #363636;
      --border-color: var(--ion-color-primary);
    }

    &.ion-invalid.ion-touched {
      --background: rgba(255, 77, 77, 0.1);
      --border-color: var(--ion-color-danger);
    }

    ion-label {
      color: #ffffff;
      font-weight: 500;
      font-size: 14px;
    }

    ion-input {
      --color: #ffffff;
      --placeholder-color: #888888;
      font-size: 16px;
      --padding-end: 48px;
    }
  }

  .password-toggle {
    --color: #888888;
    margin: 0;
    --padding-start: 8px;
    --padding-end: 8px;
    --padding-top: 8px;
    --padding-bottom: 8px;
    height: 40px;
    width: 40px;
    --border-radius: 0;

    ion-icon {
      font-size: 20px;
    }

    &:hover {
      --color: #ffffff;
    }
  }

  .error-message {
    color: var(--ion-color-danger);
    font-size: 13px;
    margin: 6px 16px 20px 16px;
    min-height: 18px;

    &::before {
      content: '⚠ ';
    }

    &.login-error {
      background: rgba(255, 77, 77, 0.1);
      border: 1px solid var(--ion-color-danger);
      border-radius: 8px;
      padding: 12px 16px;
      margin: 0 0 20px 0;
      text-align: center;
      font-weight: 500;

      &::before {
        content: '🚫 ';
      }
    }
  }

  .login-button {
    margin-top: 24px;
    --border-radius: 8px;
    height: 48px;
    font-weight: 600;
    font-size: 16px;
    --background: var(--ion-color-primary);

    &[disabled] {
      --opacity: 0.6;
    }

    ion-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}



// Responsive design
@media (max-width: 480px) {
  .login-content { padding: 16px; }
  .login-container { padding: 24px; max-width: 100%; }
  .header-section { margin-bottom: 32px; }
  .header-section .logo-icon { font-size: 56px; }
  .header-section h1 { font-size: 24px; }
  .login-form .form-item { --min-height: 48px; }
  .login-form .login-button { height: 44px; font-size: 15px; }
}

@media (min-width: 768px) {
  .login-content {
    padding: 40px;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-container {
    max-width: 420px;
    padding: 48px;
    border-radius: 16px;
  }

  .login-form .form-item {
    --min-height: 56px;
    --border-radius: 8px;
  }

  .header-section .logo-icon { font-size: 72px; }
  .header-section h1 { font-size: 32px; }
  .login-form .login-button { height: 52px; }
}

@media (min-width: 1024px) {
  .login-container {
    max-width: 440px;
    padding: 56px;
  }
  .header-section .logo-icon { font-size: 80px; }
  .header-section h1 { font-size: 36px; }
  .login-form .form-item { --min-height: 60px; }
  .login-form .login-button { height: 56px; }
}
