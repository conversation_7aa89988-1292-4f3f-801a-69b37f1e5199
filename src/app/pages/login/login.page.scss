.login-content {
  --background: var(--ion-color-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;

  .logo-icon {
    font-size: 64px;
    color: var(--ion-color-primary);
    margin-bottom: 16px;
  }

  h1 {
    color: var(--ion-color-light);
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 16px;
    margin: 0;
  }
}

.login-form {
  margin-bottom: 30px;

  .form-item {
    margin-bottom: 8px;
    --background: var(--ion-color-dark-shade);
    --border-radius: 8px;
    --border-color: var(--ion-color-medium);
    --color: var(--ion-color-light);

    &.ion-invalid {
      --border-color: var(--ion-color-danger);
    }

    ion-label {
      color: var(--ion-color-light);
      font-weight: 500;
    }

    ion-input {
      --color: var(--ion-color-light);
      --placeholder-color: var(--ion-color-medium);
    }
  }

  .password-toggle {
    --color: var(--ion-color-medium);
    margin: 0;
  }

  .error-message {
    color: var(--ion-color-danger);
    font-size: 12px;
    margin: 4px 16px 16px 16px;
    min-height: 16px;
  }

  .login-button {
    margin-top: 24px;
    --border-radius: 8px;
    height: 48px;
    font-weight: 600;

    &[disabled] {
      --opacity: 0.5;
    }
  }
}

.demo-info {
  ion-card {
    --background: var(--ion-color-dark-shade);
    --color: var(--ion-color-light);
    margin: 0;

    ion-card-header {
      padding-bottom: 8px;

      ion-card-title {
        color: var(--ion-color-primary);
        font-size: 16px;
        font-weight: 600;
      }
    }

    ion-card-content {
      padding-top: 0;

      p {
        margin: 4px 0;
        font-size: 14px;

        &.note {
          color: var(--ion-color-medium);
          font-style: italic;
          margin-top: 12px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .header-section {
    margin-bottom: 32px;

    .logo-icon {
      font-size: 48px;
    }

    h1 {
      font-size: 24px;
    }
  }
}
