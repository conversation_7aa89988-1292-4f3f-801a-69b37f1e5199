.login-content {
  --background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 94, 77, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

.login-container {
  width: 100%;
  max-width: 420px;
  padding: 24px;
  position: relative;
  z-index: 1;
}

.header-section {
  text-align: center;
  margin-bottom: 48px;

  .logo-icon {
    font-size: 72px;
    color: var(--ion-color-primary);
    margin-bottom: 20px;
    filter: drop-shadow(0 4px 8px rgba(var(--ion-color-primary-rgb), 0.3));
  }

  h1 {
    color: #ffffff;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 12px 0;
    letter-spacing: -0.5px;
  }

  p {
    color: #a0a0a0;
    font-size: 16px;
    margin: 0;
  }
}

.login-form {
  margin-bottom: 32px;

  .form-item {
    margin-bottom: 16px;
    --background: rgba(255, 255, 255, 0.05);
    --border-radius: 12px;
    --border-color: rgba(255, 255, 255, 0.1);
    --color: #ffffff;
    --min-height: 56px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      --background: rgba(255, 255, 255, 0.08);
    }

    &.ion-focused {
      --border-color: var(--ion-color-primary);
    }

    &.ion-invalid {
      --border-color: var(--ion-color-danger);
    }

    ion-label {
      color: #ffffff;
      font-weight: 600;
      font-size: 14px;
    }

    ion-input {
      --color: #ffffff;
      --placeholder-color: #888888;
      font-size: 16px;
    }
  }

  .password-toggle {
    --color: #888888;
    margin: 0;

    &:hover {
      --color: #ffffff;
    }
  }

  .error-message {
    color: var(--ion-color-danger);
    font-size: 13px;
    margin: 6px 16px 20px 16px;
    min-height: 18px;

    &::before {
      content: '⚠ ';
    }
  }

  .login-button {
    margin-top: 32px;
    --border-radius: 12px;
    height: 56px;
    font-weight: 700;
    font-size: 16px;
    --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
    --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);

    &:hover {
      --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
    }

    &[disabled] {
      --opacity: 0.6;
    }

    ion-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

.demo-info {
  ion-card {
    --background: rgba(255, 255, 255, 0.05);
    --color: #ffffff;
    margin: 0;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);

    ion-card-header {
      padding: 20px 20px 12px 20px;

      ion-card-title {
        color: var(--ion-color-primary);
        font-size: 18px;
        font-weight: 700;

        &::before {
          content: '🔑 ';
        }
      }
    }

    ion-card-content {
      padding: 0 20px 20px 20px;

      p {
        margin: 8px 0;
        font-size: 15px;
        display: flex;
        justify-content: space-between;

        strong {
          color: #ffffff;
          font-weight: 600;
        }

        &.note {
          color: #a0a0a0;
          font-style: italic;
          margin-top: 16px;
          font-size: 13px;
          justify-content: center;

          &::before {
            content: '💡 ';
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container { padding: 20px 16px; max-width: 100%; }
  .header-section { margin-bottom: 36px; }
  .header-section .logo-icon { font-size: 60px; }
  .header-section h1 { font-size: 28px; }
  .login-form .form-item { --min-height: 52px; }
  .login-form .login-button { height: 52px; font-size: 15px; }
}

@media (min-width: 768px) {
  .login-content { padding: 40px; }
  .login-container {
    max-width: 480px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 24px;
  }
  .header-section .logo-icon { font-size: 84px; }
  .header-section h1 { font-size: 36px; }
  .login-form .form-item { --min-height: 64px; }
  .login-form .login-button { height: 64px; }
}

@media (min-width: 1025px) {
  .login-container { max-width: 520px; padding: 48px; }
  .header-section .logo-icon { font-size: 96px; }
  .header-section h1 { font-size: 40px; }
  .login-form .form-item { --min-height: 68px; }
  .login-form .login-button { height: 68px; }
}
