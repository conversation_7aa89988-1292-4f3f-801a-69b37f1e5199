import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { LoadingService } from '../../services/loading.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: false
})
export class LoginPage implements OnInit, OnDestroy {
  loginForm: FormGroup;
  showPassword = false;
  loginError = '';
  private authSubscription?: Subscription;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private loadingService: LoadingService,
    private router: Router
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {
    // Check if user is already authenticated and redirect
    this.authSubscription = this.authService.isAuthenticated$.subscribe(isAuthenticated => {
      if (isAuthenticated) {
        this.router.navigate(['/tabs/orders']);
      }
    });
  }

  ngOnDestroy() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  async onSubmit() {
    // Clear previous error
    this.loginError = '';

    if (this.loginForm.valid) {
      const { email, password } = this.loginForm.value;

      await this.loadingService.present('Logging in...');

      try {
        await this.authService.login(email, password);
        // No toast notification - just let the auth service handle navigation
      } catch (error: any) {
        // Handle login errors
        let errorMessage = '';
        if (error?.code === 'auth/user-not-found') {
          errorMessage = 'No account found with this email address';
        } else if (error?.code === 'auth/wrong-password') {
          errorMessage = 'Incorrect password';
        } else if (error?.code === 'auth/invalid-email') {
          errorMessage = 'Invalid email address';
        } else if (error?.code === 'auth/user-disabled') {
          errorMessage = 'This account has been disabled';
        } else if (error?.code === 'auth/too-many-requests') {
          errorMessage = 'Too many failed attempts. Please try again later';
        } else {
          errorMessage = error?.message || 'Login failed. Please try again';
        }

        this.loginError = errorMessage;
      } finally {
        await this.loadingService.dismiss();
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  getErrorMessage(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return 'Password must be at least 6 characters long';
      }
    }
    return '';
  }
}
