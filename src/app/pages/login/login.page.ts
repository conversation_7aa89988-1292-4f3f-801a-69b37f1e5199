import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { LoadingService } from '../../services/loading.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: false
})
export class LoginPage implements OnInit {
  loginForm: FormGroup;
  showPassword = false;
  loginError = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private loadingService: LoadingService
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {}

  async onSubmit() {
    // Clear previous error
    this.loginError = '';

    if (this.loginForm.valid) {
      const { email, password } = this.loginForm.value;

      await this.loadingService.present('Logging in...');

      try {
        await this.authService.login(email, password);
        // No toast notification - just let the auth service handle navigation
      } catch (error: any) {
        // Handle login errors
        if (error?.code === 'auth/user-not-found') {
          this.loginError = 'No account found with this email address';
        } else if (error?.code === 'auth/wrong-password') {
          this.loginError = 'Incorrect password';
        } else if (error?.code === 'auth/invalid-email') {
          this.loginError = 'Invalid email address';
        } else if (error?.code === 'auth/user-disabled') {
          this.loginError = 'This account has been disabled';
        } else if (error?.code === 'auth/too-many-requests') {
          this.loginError = 'Too many failed attempts. Please try again later';
        } else {
          this.loginError = error?.message || 'Login failed. Please try again';
        }
      } finally {
        await this.loadingService.dismiss();
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  getErrorMessage(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return 'Password must be at least 6 characters long';
      }
    }
    return '';
  }
}
