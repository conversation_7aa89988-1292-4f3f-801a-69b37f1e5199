import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AlertController, NavController } from '@ionic/angular';
import { finalize } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../../models/order.model';
import { LoadingService } from '../../services/loading.service';
import { OrderService } from '../../services/order.service';
import { ToastService } from '../../services/toast.service';
import { UtilityService } from '../../services/utility.service';

@Component({
    selector: 'app-order-detail',
    templateUrl: './order-detail.page.html',
    styleUrls: ['./order-detail.page.scss'],
    standalone: false
})
export class OrderDetailPage implements OnInit {
  order: Order | undefined;
  OrderStatus = OrderStatus;

  constructor(
    private route: ActivatedRoute,
    private orderService: OrderService,
    private navCtrl: NavController,
    private utilityService: UtilityService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    private alertController: AlertController
  ) { }

  ngOnInit() {
    this.loadOrderDetails();
  }

  async loadOrderDetails() {
    const orderId = this.route.snapshot.paramMap.get('id');

    if (!orderId) {
      this.toastService.presentErrorToast('Order ID not found');
      this.navCtrl.navigateBack('/tabs/orders');
      return;
    }

    await this.loadingService.present('Loading order details...');
    this.orderService.getOrderById(orderId)
      .pipe(finalize(() => this.loadingService.dismiss()))
      .subscribe({
        next: (order) => {
          if (order) {
            this.order = order;
          } else {
            this.toastService.presentErrorToast('Order not found');
            this.navCtrl.navigateBack('/tabs/orders');
          }
        },
        error: (err) => {
          this.toastService.presentErrorToast('Failed to load order details');
          console.error('Error loading order details:', err);
          this.navCtrl.navigateBack('/tabs/orders');
        }
      });
  }

  getStatusColor(status: OrderStatus): string {
    return this.utilityService.getStatusColor(status);
  }

  getFormattedDate(date: Date): string {
    return this.utilityService.formatDateTime(date);
  }

  formatCurrency(value: number): string {
    return this.utilityService.formatCurrency(value);
  }

  goBack() {
    this.navCtrl.back();
  }

  async acceptOrder() {
    if (!this.order) return;

    await this.loadingService.present('Accepting order...');
    try {
      const result = await this.orderService.acceptOrder(this.order.id);
      result.pipe(finalize(() => this.loadingService.dismiss()))
        .subscribe({
          next: () => {
            this.toastService.presentSuccessToast('Order accepted successfully');
            this.loadOrderDetails();
          },
          error: (error: any) => {
            this.toastService.presentErrorToast('Failed to accept order');
            console.error('Error accepting order:', error);
          }
        });
    } catch (error) {
      await this.loadingService.dismiss();
      this.toastService.presentErrorToast('Failed to accept order');
      console.error('Error accepting order:', error);
    }
  }

  async rejectOrder() {
    if (!this.order) return;

    const alert = await this.alertController.create({
      header: 'Reject Order',
      message: 'Please provide a reason for rejecting this order',
      inputs: [
        {
          name: 'reason',
          type: 'text',
          placeholder: 'Reason for rejection'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Reject',
          handler: (data) => {
            this.confirmRejectOrder(data.reason);
          }
        }
      ]
    });

    await alert.present();
  }

  private async confirmRejectOrder(reason: string) {
    if (!this.order) return;

    await this.loadingService.present('Rejecting order...');
    try {
      const result = await this.orderService.rejectOrder(this.order.id, reason as RejectionReason);
      result.pipe(finalize(() => this.loadingService.dismiss()))
        .subscribe({
          next: () => {
            this.toastService.presentSuccessToast('Order rejected successfully');
            this.loadOrderDetails();
          },
          error: (error: any) => {
            this.toastService.presentErrorToast('Failed to reject order');
            console.error('Error rejecting order:', error);
          }
        });
    } catch (error) {
      await this.loadingService.dismiss();
      this.toastService.presentErrorToast('Failed to reject order');
      console.error('Error rejecting order:', error);
    }
  }
}
