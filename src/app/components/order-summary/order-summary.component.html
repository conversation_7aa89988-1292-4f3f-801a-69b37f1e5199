<div class="order-summary">
  <div class="order-items">
    <!-- Main items -->
    <app-order-item *ngFor="let item of order.items"
      [quantity]="item.quantity + 'x'"
      [name]="item.name"
      [price]="item.price">
    </app-order-item>

    <!-- Item notes -->
    <ng-container *ngFor="let item of order.items">
      <app-order-item *ngIf="item.notes"
        [name]="item.notes"
        [isModifier]="true"
        [showPrice]="false">
      </app-order-item>
    </ng-container>
  </div>

  <div class="order-total">
    {{ formatCurrency(order.total) }}
  </div>

  <div *ngIf="showSpecialInstructions && order.specialInstructions" class="special-instructions">
    {{ order.specialInstructions }}
  </div>

  <div *ngIf="showRejectionReason && order.rejectionReason" class="rejection-reason">
    Reason: {{ order.rejectionReason }}
  </div>
</div>
