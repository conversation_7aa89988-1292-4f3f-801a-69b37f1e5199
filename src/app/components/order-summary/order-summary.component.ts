import { Component, Input, OnInit } from '@angular/core';
import { Order } from '../../models/order.model';

@Component({
    selector: 'app-order-summary',
    templateUrl: './order-summary.component.html',
    styleUrls: ['./order-summary.component.scss'],
    standalone: false
})
export class OrderSummaryComponent implements OnInit {
  @Input() order!: Order; // Using the non-null assertion operator
  @Input() showSpecialInstructions: boolean = true;
  @Input() showRejectionReason: boolean = false;

  constructor() { }

  ngOnInit() {}

  formatCurrency(amount: number): string {
    return `${amount.toFixed(2)} RON`;
  }
}
