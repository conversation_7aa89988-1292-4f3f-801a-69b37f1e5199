<div class="item-row" [ngClass]="{'modifier-row': isModifier}">
  <div class="item-quantity">{{ quantity }}</div>
  <div class="item-details">
    <div [ngClass]="{'item-name': !isModifier, 'modifier-name': isModifier}">{{ name }}</div>
  </div>
  <div *ngIf="showPrice && (price > 0 || !isModifier)"
       [ngClass]="{'item-price': !isModifier, 'item-price modifier-price': isModifier}">
    <ng-container *ngIf="isModifier && price > 0">+</ng-container>{{ formatCurrency(price) }}
  </div>
  <div *ngIf="!showPrice || (isModifier && price === 0)" class="item-price"></div>
</div>
