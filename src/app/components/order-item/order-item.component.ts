import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-order-item',
    templateUrl: './order-item.component.html',
    styleUrls: ['./order-item.component.scss'],
    standalone: false
})
export class OrderItemComponent implements OnInit {
  @Input() quantity: string = '';
  @Input() name: string = '';
  @Input() price: number = 0;
  @Input() isModifier: boolean = false;
  @Input() showPrice: boolean = true;

  constructor() { }

  ngOnInit() {}

  formatCurrency(amount: number): string {
    return `${amount.toFixed(2)} RON`;
  }
}
