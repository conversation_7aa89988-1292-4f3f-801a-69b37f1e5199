.item-row {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.modifier-row {
  margin-bottom: 2px;
}

.item-quantity {
  width: 30px;
  font-weight: 500;
  color: var(--text-light);
  margin-right: 8px;
  padding-left: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.modifier-name {
  font-size: 12px;
  color: var(--text-secondary);
}

.item-price {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: right;
  min-width: 90px;
}

.modifier-price {
  font-size: 12px;
}