export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected'
}

export enum RejectionReason {
  OUT_OF_STOCK = 'Out of stock',
  KITCHEN_CLOSED = 'Kitchen closed',
  TOO_BUSY = 'Too busy',
  INVALID_ORDER = 'Invalid order',
  CUSTOMER_CANCELLED = 'Customer cancelled',
  OTHER = 'Other'
}

// Firestore interfaces matching the simplified database structure
export interface Venue {
  id: string;
  name: string;
}

export interface Waiter {
  id: string;
  venueId: string;
  email: string;
  name: {
    first: string;
    last: string;
  };
  role: 'admin' | 'manager' | 'waiter';
  permissions: {
    canViewOrders: boolean;
    canUpdateOrders: boolean;
    canDeleteOrders: boolean;
    canManageMenu: boolean;
    canManageStaff: boolean;
  };
  isActive: boolean;
  lastLogin?: Date;
}

export interface Table {
  id: string;
  venueId: string;
  tableNumber: string;
  name: string;
}

export interface Product {
  id: string;
  venueId: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  isModifier: boolean;
  isActive: boolean;
}

export interface Modifier {
  id: string;
  name: string;
  price: number;
}

// Simplified item structure for display in orders
export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  modifiers?: Modifier[];
  notes?: string;
}

export interface Order {
  id: string;
  venueId: string;
  tableId: string;
  tableName: string;
  orderNumber: string;
  status: OrderStatus;
  items: OrderItem[]; // Simplified items for display
  total: number; // Simple total for display
  assignedWaiter?: string;
  specialInstructions?: string;
  rejectionReason?: string;
  rejectionNote?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Keep the Firestore interfaces separate for database operations
export interface FirestoreOrderItem {
  id: string;
  // orderId not needed since it's a subcollection under the order
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  specialInstructions?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FirestoreOrder {
  id: string;
  venueId: string;
  tableId: string;
  tableName: string;
  orderNumber: string;
  status: OrderStatus;
  totals: {
    subtotal: number;
    tips: number;
    discount: number;
    total: number;
    currency: string;
  };
  assignedWaiter?: string;
  specialInstructions?: string;
  rejectionReason?: string;
  rejectionNote?: string;
  createdAt: Date;
  updatedAt: Date;
}
