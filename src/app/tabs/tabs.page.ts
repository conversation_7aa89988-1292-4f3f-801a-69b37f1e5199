import { Component } from '@angular/core';
import { OrderService } from '../services/order.service';
import { AuthService } from '../services/auth.service';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';

@Component({
    selector: 'app-tabs',
    templateUrl: 'tabs.page.html',
    styleUrls: ['tabs.page.scss'],
    standalone: false
})
export class TabsPage {
  pendingOrdersCount$: Observable<number> = of(0);

  constructor(
    private orderService: OrderService,
    private authService: AuthService
  ) {
    this.pendingOrdersCount$ = this.orderService.getPendingOrders().pipe(
      map(orders => orders.length)
    );
  }

  async logout() {
    await this.authService.logout();
  }
}
