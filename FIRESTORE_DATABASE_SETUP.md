# Firestore Database Setup for Order Flow QR

This document outlines the complete Firestore database structure for the Order Flow QR system.

## Collections Overview

The database consists of 6 main collections:
- `venues` - Restaurant/venue information
- `waiters` - Staff members who can manage orders
- `tables` - Physical tables in the venue
- `products` - Menu items and their details
- `orders` - Customer orders
- `order_items` - Individual items within orders

## Collection Structures

### 1. Venues Collection (`venues`)

```typescript
interface Venue {
  id: string;
  name: string;
}
```

### 2. Waiters Collection (`waiters`)

```typescript
interface Waiter {
  id: string;
  venueId: string; // Reference to venue
  email: string;
  name: {
    first: string;
    last: string;
  };
  role: 'admin' | 'manager' | 'waiter';
  permissions: {
    canViewOrders: boolean;
    canUpdateOrders: boolean;
    canDeleteOrders: boolean;
    canManageMenu: boolean;
    canManageStaff: boolean;
  };
  isActive: boolean;
  lastLogin?: Timestamp;
}
```

### 3. Tables Collection (`tables`)

```typescript
interface Table {
  id: string;
  venueId: string; // Reference to venue  tableNumber: string; // "1", "A1", "VIP-1", etc.
  name: string; // "Table 1", "Booth A1", etc.
}
```

### 4. Products Collection (`products`)

```typescript
interface Product {
  id: string;
  venueId: string; // Reference to venue
  name: string;  description: string;
  price: number;
  currency: string; // "RON"
  isModifier: boolean;
  isActive: boolean;
}
```

### 5. Orders Collection (`orders`)

```typescript
interface Order {
  id: string;
  venueId: string; // Reference to venue
  tableId: string; // Reference to table
  tableName: string; // Denormalized for quick access
  orderNumber: string; // Human-readable order number
  status: 'pending' | 'confirmed' | 'rejected';
  totals: {
    subtotal: number;
    tips: number;
    discount: number;
    total: number;
    currency: string;
  };
  assignedWaiter?: string; // Reference to waiter
  specialInstructions?: string;
  rejectionReason?: string;
  rejectionNote?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 6. Order Items Collection (`order_items`)

```typescript
interface OrderItem {
  id: string;
  orderId: string; // Reference to order
  productId: string; // Reference to product
  productName: string; // Denormalized for historical data
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  selectedModifiers: SelectedModifier[];
  specialInstructions?: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'cancelled';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

interface SelectedModifier {
  modifierId: string;
  modifierName: string;
  optionId: string;
  optionName: string;
  price: number;
}
```

## Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Venues - Only authenticated users can read
    match /venues/{venueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        resource.data.waiters[request.auth.uid].role in ['admin', 'manager'];
    }

    // Waiters - Only authenticated users can read their own data
    match /waiters/{waiterId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == waiterId ||
         get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager']);
    }

    // Tables - Authenticated users can read, admins/managers can write
    match /tables/{tableId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }

    // Products - Authenticated users can read, admins/managers can write
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }

    // Orders - Authenticated users can read and write
    match /orders/{orderId} {
      allow read, write: if request.auth != null;
    }

    // Order Items - Authenticated users can read and write
    match /order_items/{orderItemId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Database Setup Steps

### Step 1: Create Collections and Sample Data

You can use the Firebase Console or the Firebase Admin SDK to create the collections and add sample data.

#### Sample Venue Data

```json
{
  "id": "venue_001",
  "name": "Bella Vista Restaurant",
  "address": {
    "street": "Strada Victoriei 123",
    "city": "Bucharest",
    "state": "Bucharest",
    "zipCode": "010065",
    "country": "Romania"
  },
  "contact": {
    "phone": "+40 21 123 4567",
    "email": "<EMAIL>",
    "website": "https://bellavista.ro"
  },
  "settings": {
    "currency": "RON",
    "timezone": "Europe/Bucharest",
    "taxRate": 0.19,
    "serviceCharge": 0.10
  },
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### Sample Waiter Data

```json
{
  "id": "waiter_001",
  "venueId": "venue_001",
  "email": "<EMAIL>",
  "name": {
    "first": "Admin",
    "last": "User"
  },
  "role": "admin",
  "permissions": {
    "canViewOrders": true,
    "canUpdateOrders": true,
    "canDeleteOrders": true,
    "canManageMenu": true,
    "canManageStaff": true
  },
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### Sample Table Data

```json
{
  "id": "table_001",
  "venueId": "venue_001",
  "tableNumber": "1",
  "displayName": "Table 1",
  "capacity": 4,
  "location": {
    "section": "Main Floor",
    "zone": "Non-smoking",
    "floor": 1
  },
  "qrCode": {
    "code": "TBL001_QR_CODE",
    "url": "https://your-app.com/order?table=table_001",
    "generatedAt": "2024-01-01T00:00:00Z"
  },
  "status": "available",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```