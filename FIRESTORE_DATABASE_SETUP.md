# Firestore Database Setup for Order Flow QR

This document outlines the complete Firestore database structure for the Order Flow QR system.

## Collections Overview

The database consists of 5 main collections:
- `venues` - Restaurant/venue information
- `waiters` - Staff members who can manage orders
- `tables` - Physical tables in the venue
- `products` - Menu items and their details
- `orders` - Customer orders
  - `order_items` (subcollection) - Individual items within each order

## Collection Structures

### 1. Venues Collection (`venues`)

```typescript
interface Venue {
  id: string;
  name: string;
}
```

### 2. Waiters Collection (`waiters`)

```typescript
interface Waiter {
  id: string;
  venueId: string; // Reference to venue
  email: string;
  name: {
    first: string;
    last: string;
  };
  role: 'admin' | 'manager' | 'waiter';
  permissions: {
    canViewOrders: boolean;
    canUpdateOrders: boolean;
    canDeleteOrders: boolean;
    canManageMenu: boolean;
    canManageStaff: boolean;
  };
  isActive: boolean;
  lastLogin?: Timestamp;
}
```

### 3. Tables Collection (`tables`)

```typescript
interface Table {
  id: string;
  venueId: string; // Reference to venue
  tableNumber: string; // "1", "A1", "VIP-1", etc.
  name: string; // "Table 1", "Booth A1", etc.
}
```

### 4. Products Collection (`products`)

```typescript
interface Product {
  id: string;
  venueId: string; // Reference to venue
  name: string;
  description: string;
  price: number;
  currency: string; // "RON"
  isModifier: boolean;
  isActive: boolean;
}
```

### 5. Orders Collection (`orders`)

```typescript
interface Order {
  id: string;
  venueId: string; // Reference to venue
  tableId: string; // Reference to table
  tableName: string; // Denormalized for quick access
  orderNumber: string; // Human-readable order number
  status: 'pending' | 'confirmed' | 'rejected';
  totals: {
    subtotal: number;
    tips: number;
    discount: number;
    total: number;
    currency: string;
  };
  assignedWaiter?: string; // Reference to waiter
  specialInstructions?: string;
  rejectionReason?: string;
  rejectionNote?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 6. Order Items Subcollection (`orders/{orderId}/order_items`)

```typescript
interface OrderItem {
  id: string;
  // orderId not needed since it's a subcollection under the order
  productId: string; // Reference to product
  productName: string; // Denormalized for historical data
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  currency: string;
  specialInstructions?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

**Path Structure:**
- Collection: `orders`
- Document: `{orderId}`
- Subcollection: `order_items`
- Document: `{orderItemId}`

## Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Venues - Only authenticated users can read
    match /venues/{venueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        resource.data.waiters[request.auth.uid].role in ['admin', 'manager'];
    }

    // Waiters - Only authenticated users can read their own data
    match /waiters/{waiterId} {
      allow read, write: if request.auth != null &&
        (request.auth.uid == waiterId ||
         get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager']);
    }

    // Tables - Authenticated users can read, admins/managers can write
    match /tables/{tableId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }

    // Products - Authenticated users can read, admins/managers can write
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }

    // Orders - Authenticated users can read and write
    match /orders/{orderId} {
      allow read, write: if request.auth != null;
    }

    // Order Items (subcollection) - Authenticated users can read and write
    match /orders/{orderId}/order_items/{orderItemId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Database Setup Steps

### Step 1: Create Collections and Sample Data

You can use the Firebase Console or the Firebase Admin SDK to create the collections and add sample data.

#### Sample Venue Data

```json
{
  "id": "venue_001",
  "name": "Bella Vista Restaurant"
}
```

#### Sample Waiter Data

```json
{
  "id": "waiter_001",
  "venueId": "venue_001",
  "email": "<EMAIL>",
  "name": {
    "first": "Admin",
    "last": "User"
  },
  "role": "admin",
  "permissions": {
    "canViewOrders": true,
    "canUpdateOrders": true,
    "canDeleteOrders": true,
    "canManageMenu": true,
    "canManageStaff": true
  },
  "isActive": true
}
```

#### Sample Table Data

```json
{
  "id": "table_001",
  "venueId": "venue_001",
  "tableNumber": "1",
  "name": "Table 1"
}
```

#### Sample Product Data

```json
{
  "id": "product_001",
  "venueId": "venue_001",
  "name": "Pizza Margherita",
  "description": "Classic pizza with tomato sauce, mozzarella, and fresh basil",
  "price": 25.99,
  "currency": "RON",
  "isModifier": false,
  "isActive": true
}
```

#### Sample Order Data

```json
{
  "id": "order_001",
  "venueId": "venue_001",
  "tableId": "table_001",
  "tableName": "Table 1",
  "orderNumber": "ORD-001",
  "status": "pending",
  "totals": {
    "subtotal": 25.99,
    "tips": 2.60,
    "discount": 0,
    "total": 28.59,
    "currency": "RON"
  },
  "specialInstructions": "No onions please",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### Sample Order Item Data (Subcollection)

**Path:** `orders/order_001/order_items/order_item_001`

```json
{
  "id": "order_item_001",
  "productId": "product_001",
  "productName": "Pizza Margherita",
  "quantity": 1,
  "unitPrice": 25.99,
  "totalPrice": 25.99,
  "currency": "RON",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### Step 2: Create Indexes

Create the following composite indexes in Firestore:

1. **Orders Collection**:
   - `venueId` (Ascending) + `status` (Ascending) + `createdAt` (Descending)
   - `venueId` (Ascending) + `tableId` (Ascending) + `createdAt` (Descending)
   - `venueId` (Ascending) + `assignedWaiter` (Ascending) + `status` (Ascending)

2. **Order Items Subcollection**:
   - Since order items are now subcollections under orders, most queries will be scoped to a specific order
   - No additional indexes needed for basic queries within an order

3. **Products Collection**:
   - `venueId` (Ascending) + `isActive` (Ascending)

4. **Tables Collection**:
   - `venueId` (Ascending)

## Benefits of Subcollection Structure

### **1. Better Data Organization**
- Order items are naturally grouped under their parent order
- Cleaner document paths: `orders/{orderId}/order_items/{itemId}`
- No need for `orderId` field in order items

### **2. Improved Query Performance**
- Queries for order items are automatically scoped to a specific order
- Faster reads when fetching all items for an order
- Better caching and indexing by Firestore

### **3. Atomic Operations**
- Can use batch writes to update order and items together
- Better consistency when modifying order data

### **4. Simplified Security Rules**
- Order items inherit security context from parent order
- Easier to manage permissions

### **5. Cost Optimization**
- More efficient queries (no need to filter by orderId)
- Better document locality for related data

## Querying Examples

### **Get all items for an order:**
```typescript
const orderItemsRef = collection(db, 'orders', orderId, 'order_items');
const orderItemsSnapshot = await getDocs(orderItemsRef);
```

### **Add item to order:**
```typescript
const orderItemRef = doc(db, 'orders', orderId, 'order_items', itemId);
await setDoc(orderItemRef, itemData);
```

### **Update order and items atomically:**
```typescript
const batch = writeBatch(db);
batch.update(doc(db, 'orders', orderId), orderUpdates);
batch.set(doc(db, 'orders', orderId, 'order_items', itemId), itemData);
await batch.commit();
```