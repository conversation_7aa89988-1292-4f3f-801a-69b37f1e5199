// Database initialization script for Order Flow QR
// Run this script to populate your Firestore database with sample data

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, Timestamp } from 'firebase/firestore';

// Import your Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...');

  try {
    // 1. Create sample venue
    console.log('📍 Creating venue...');
    await setDoc(doc(db, 'venues', 'venue_001'), {
      id: 'venue_001',
      name: 'Bella Vista Restaurant',
      address: {
        street: 'Strada Victoriei 123',
        city: 'Bucharest',
        state: 'Bucharest',
        zipCode: '010065',
        country: 'Romania'
      },
      contact: {
        phone: '+40 21 123 4567',
        email: '<EMAIL>',
        website: 'https://bellavista.ro'
      },
      settings: {
        currency: 'RON',
        timezone: 'Europe/Bucharest',
        taxRate: 0.19,
        serviceCharge: 0.10
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    // 2. Create sample waiter/admin
    console.log('👨‍💼 Creating admin waiter...');
    await setDoc(doc(db, 'waiters', 'waiter_001'), {
      id: 'waiter_001',
      venueId: 'venue_001',
      email: '<EMAIL>',
      name: {
        first: 'Admin',
        last: 'User'
      },
      role: 'admin',
      permissions: {
        canViewOrders: true,
        canUpdateOrders: true,
        canDeleteOrders: true,
        canManageMenu: true,
        canManageStaff: true
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    // 3. Create sample tables
    console.log('🪑 Creating tables...');
    for (let i = 1; i <= 10; i++) {
      const tableId = `table_${i.toString().padStart(3, '0')}`;
      await setDoc(doc(db, 'tables', tableId), {
        id: tableId,
        venueId: 'venue_001',
        tableNumber: i.toString(),
        displayName: `Table ${i}`,
        capacity: i <= 5 ? 4 : 6, // First 5 tables seat 4, rest seat 6
        location: {
          section: i <= 5 ? 'Main Floor' : 'Terrace',
          zone: 'Non-smoking',
          floor: 1
        },
        qrCode: {
          code: `TBL${i.toString().padStart(3, '0')}_QR`,
          url: `https://your-app.com/order?table=${tableId}`,
          generatedAt: Timestamp.now()
        },
        status: 'available',
        isActive: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    // 4. Create sample products
    console.log('🍕 Creating menu products...');
    
    const products = [
      {
        id: 'product_001',
        name: 'Pizza Margherita',
        description: 'Classic pizza with tomato sauce, mozzarella, and fresh basil',
        category: 'Main Course',
        subcategory: 'Pizza',
        price: 25.99,
        allergens: ['gluten', 'dairy'],
        dietary: ['vegetarian'],
        preparationTime: 15
      },
      {
        id: 'product_002',
        name: 'Pasta Carbonara',
        description: 'Creamy pasta with bacon, eggs, and parmesan cheese',
        category: 'Main Course',
        subcategory: 'Pasta',
        price: 22.50,
        allergens: ['gluten', 'dairy', 'eggs'],
        dietary: [],
        preparationTime: 12
      },
      {
        id: 'product_003',
        name: 'Caesar Salad',
        description: 'Fresh romaine lettuce with caesar dressing, croutons, and parmesan',
        category: 'Appetizers',
        subcategory: 'Salads',
        price: 18.00,
        allergens: ['gluten', 'dairy'],
        dietary: ['vegetarian'],
        preparationTime: 8
      },
      {
        id: 'product_004',
        name: 'Coca Cola',
        description: 'Classic Coca Cola 330ml',
        category: 'Beverages',
        subcategory: 'Soft Drinks',
        price: 5.50,
        allergens: [],
        dietary: ['vegan'],
        preparationTime: 1
      },
      {
        id: 'product_005',
        name: 'Tiramisu',
        description: 'Traditional Italian dessert with coffee and mascarpone',
        category: 'Desserts',
        subcategory: 'Italian',
        price: 15.00,
        allergens: ['gluten', 'dairy', 'eggs'],
        dietary: ['vegetarian'],
        preparationTime: 5
      }
    ];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      await setDoc(doc(db, 'products', product.id), {
        ...product,
        venueId: 'venue_001',
        currency: 'RON',
        images: [`https://example.com/${product.id}.jpg`],
        modifiers: product.category === 'Main Course' ? [
          {
            id: 'size_modifier',
            name: 'Size',
            type: 'single',
            required: true,
            options: [
              {
                id: 'regular',
                name: 'Regular',
                price: 0,
                isDefault: true,
                isAvailable: true
              },
              {
                id: 'large',
                name: 'Large',
                price: 5.00,
                isAvailable: true
              }
            ]
          }
        ] : [],
        availability: {
          isAvailable: true,
          availableDays: [0, 1, 2, 3, 4, 5, 6],
          availableHours: {
            start: '11:00',
            end: '23:00'
          }
        },
        isActive: true,
        sortOrder: i + 1,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    }

    // 5. Create a sample order
    console.log('📋 Creating sample order...');
    const orderId = 'order_001';
    await setDoc(doc(db, 'orders', orderId), {
      id: orderId,
      venueId: 'venue_001',
      tableId: 'table_001',
      tableNumber: '1',
      orderNumber: 'ORD-001',
      customer: {
        name: 'John Doe',
        phone: '+40 ***********',
        specialRequests: 'No onions please'
      },
      status: 'pending',
      priority: 'normal',
      timing: {
        orderedAt: Timestamp.now()
      },
      totals: {
        subtotal: 31.49,
        tax: 5.98,
        serviceCharge: 3.15,
        discount: 0,
        total: 40.62,
        currency: 'RON'
      },
      payment: {
        status: 'pending'
      },
      specialInstructions: 'Table by the window if possible',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    // 6. Create sample order items
    console.log('🍽️ Creating sample order items...');
    await setDoc(doc(db, 'order_items', 'order_item_001'), {
      id: 'order_item_001',
      orderId: orderId,
      productId: 'product_001',
      productName: 'Pizza Margherita',
      quantity: 1,
      unitPrice: 25.99,
      totalPrice: 25.99,
      currency: 'RON',
      selectedModifiers: [
        {
          modifierId: 'size_modifier',
          modifierName: 'Size',
          optionId: 'regular',
          optionName: 'Regular',
          price: 0
        }
      ],
      status: 'pending',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'order_items', 'order_item_002'), {
      id: 'order_item_002',
      orderId: orderId,
      productId: 'product_004',
      productName: 'Coca Cola',
      quantity: 1,
      unitPrice: 5.50,
      totalPrice: 5.50,
      currency: 'RON',
      selectedModifiers: [],
      status: 'pending',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Database initialization completed successfully!');
    console.log('📊 Created:');
    console.log('  - 1 venue');
    console.log('  - 1 admin waiter');
    console.log('  - 10 tables');
    console.log('  - 5 products');
    console.log('  - 1 sample order with 2 items');
    console.log('');
    console.log('🔐 Test login credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123 (set this in Firebase Auth)');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
  }
}

// Run the initialization
initializeDatabase().catch(console.error);
