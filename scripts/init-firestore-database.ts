// Database initialization script for Order Flow QR
// Run this script to populate your Firestore database with sample data

import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc, Timestamp } from 'firebase/firestore';

// Import your Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...');

  try {
    // 1. Create sample venue
    console.log('📍 Creating venue...');
    await setDoc(doc(db, 'venues', 'venue_001'), {
      id: 'venue_001',
      name: 'Bella Vista Restaurant'
    });

    // 2. Create sample waiter/admin
    console.log('👨‍💼 Creating admin waiter...');
    await setDoc(doc(db, 'waiters', 'waiter_001'), {
      id: 'waiter_001',
      venueId: 'venue_001',
      email: '<EMAIL>',
      name: {
        first: 'Admin',
        last: 'User'
      },
      role: 'admin',
      permissions: {
        canViewOrders: true,
        canUpdateOrders: true,
        canDeleteOrders: true,
        canManageMenu: true,
        canManageStaff: true
      },
      isActive: true
    });

    // 3. Create sample tables
    console.log('🪑 Creating tables...');
    for (let i = 1; i <= 10; i++) {
      const tableId = `table_${i.toString().padStart(3, '0')}`;
      await setDoc(doc(db, 'tables', tableId), {
        id: tableId,
        venueId: 'venue_001',
        tableNumber: i.toString(),
        name: `Table ${i}`
      });
    }

    // 4. Create sample products
    console.log('🍕 Creating menu products...');

    const products = [
      {
        id: 'product_001',
        name: 'Pizza Margherita',
        description: 'Classic pizza with tomato sauce, mozzarella, and fresh basil',
        price: 25.99,
        isModifier: false
      },
      {
        id: 'product_002',
        name: 'Pasta Carbonara',
        description: 'Creamy pasta with bacon, eggs, and parmesan cheese',
        price: 22.50,
        isModifier: false
      },
      {
        id: 'product_003',
        name: 'Caesar Salad',
        description: 'Fresh romaine lettuce with caesar dressing, croutons, and parmesan',
        price: 18.00,
        isModifier: false
      },
      {
        id: 'product_004',
        name: 'Coca Cola',
        description: 'Classic Coca Cola 330ml',
        price: 5.50,
        isModifier: false
      },
      {
        id: 'product_005',
        name: 'Tiramisu',
        description: 'Traditional Italian dessert with coffee and mascarpone',
        price: 15.00,
        isModifier: false
      },
      {
        id: 'product_006',
        name: 'Extra Cheese',
        description: 'Additional cheese topping',
        price: 3.00,
        isModifier: true
      }
    ];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      await setDoc(doc(db, 'products', product.id), {
        ...product,
        venueId: 'venue_001',
        currency: 'RON',
        isActive: true
      });
    }

    // 5. Create a sample order
    console.log('📋 Creating sample order...');
    const orderId = 'order_001';
    await setDoc(doc(db, 'orders', orderId), {
      id: orderId,
      venueId: 'venue_001',
      tableId: 'table_001',
      tableName: 'Table 1',
      orderNumber: 'ORD-001',
      status: 'pending',
      totals: {
        subtotal: 25.99,
        tips: 2.60,
        discount: 0,
        total: 28.59,
        currency: 'RON'
      },
      specialInstructions: 'No onions please',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    // 6. Create sample order items as subcollection
    console.log('🍽️ Creating sample order items...');
    await setDoc(doc(db, 'orders', orderId, 'order_items', 'order_item_001'), {
      id: 'order_item_001',
      productId: 'product_001',
      productName: 'Pizza Margherita',
      quantity: 1,
      unitPrice: 25.99,
      totalPrice: 25.99,
      currency: 'RON',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Database initialization completed successfully!');
    console.log('📊 Created:');
    console.log('  - 1 venue');
    console.log('  - 1 admin waiter');
    console.log('  - 10 tables');
    console.log('  - 6 products (5 regular + 1 modifier)');
    console.log('  - 1 sample order with 1 item');
    console.log('');
    console.log('🔐 Test login credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123 (set this in Firebase Auth)');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
  }
}

// Run the initialization
initializeDatabase().catch(console.error);
